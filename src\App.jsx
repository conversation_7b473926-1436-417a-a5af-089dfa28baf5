import React, { useRef, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Stage } from '@react-three/drei';
import CanvasEditor from './components/3dCanvas'; // <-- Fabric + Tools
import ShirtModel from './components/ShirtModel';

const App = () => {
  const fabricRef = useRef(null);
  const [canvasEl, setCanvasEl] = useState(null);

  const handleCanvasUpdate = () => {
    if (fabricRef.current) {
      setCanvasEl(fabricRef.current.getElement());
    }
  };

  return (
    <div className="flex flex-col md:flex-row h-screen w-full overflow-hidden">
      {/* LEFT: 3D Shirt Preview */}
      <div className="w-full md:w-1/2 h-1/2 md:h-full bg-gray-900">
        <Canvas camera={{ position: [0, 0, 4.5] }}>
          <ambientLight intensity={0.7} />
          <directionalLight position={[5, 5, 5]} />
          <Stage environment="city" intensity={0.6}>
            <ShirtModel canvas={canvasEl} />
          </Stage>
          <OrbitControls />
        </Canvas>
      </div>

      {/* RIGHT: Design Tools + Canvas */}
      <div className="w-full md:w-1/2 h-1/2 md:h-full bg-white p-4 overflow-y-auto">
        <h1 className="text-2xl font-semibold mb-4 text-gray-800">🎨 Design Your Shirt</h1>

        <CanvasEditor fabricRef={fabricRef} onUpdate={handleCanvasUpdate} />
      </div>
    </div>
  );
};

export default App;
