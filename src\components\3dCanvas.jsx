import React, { useEffect, useRef } from 'react';
import { fabric } from 'fabric';


const CanvasEditor = ({ fabricRef, onUpdate }) => {
  const canvasEl = useRef(null);
  const history = useRef([]);
  const redoStack = useRef([]);

  useEffect(() => {
    const canvas = new fabric.Canvas(canvasEl.current, {
      width: 1024,
      height: 1024,
      backgroundColor: '#ffffff',
    });

    fabricRef.current = canvas;
    saveHistory();

    canvas.on('object:added', saveHistory);
    canvas.on('object:modified', saveHistory);
    canvas.on('object:removed', saveHistory);

    function saveHistory() {
      history.current.push(JSON.stringify(canvas));
      redoStack.current = []; // Clear redo stack on new change
      onUpdate();
    }

    return () => canvas.dispose();
  }, []);

  const handleUndo = () => {
    if (history.current.length > 1) {
      redoStack.current.push(history.current.pop());
      const prev = history.current[history.current.length - 1];
      fabricRef.current.loadFromJSON(prev, () => {
        fabricRef.current.renderAll();
        onUpdate();
      });
    }
  };

  const handleRedo = () => {
    if (redoStack.current.length > 0) {
      const state = redoStack.current.pop();
      history.current.push(state);
      fabricRef.current.loadFromJSON(state, () => {
        fabricRef.current.renderAll();
        onUpdate();
      });
    }
  };

  const handleAddText = () => {
    const text = new fabric.Textbox('New Text', {
      left: 100,
      top: 100,
      fill: '#000',
      fontSize: 24,
    });
    fabricRef.current.add(text);
  };

  const handleAddRect = () => {
    const rect = new fabric.Rect({
      width: 120,
      height: 80,
      fill: '#34D399',
      left: 150,
      top: 150,
    });
    fabricRef.current.add(rect);
  };

  const handleAddCircle = () => {
    const circle = new fabric.Circle({
      radius: 50,
      fill: '#3B82F6',
      left: 200,
      top: 200,
    });
    fabricRef.current.add(circle);
  };

  const handleAddImage = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function (f) {
      fabric.Image.fromURL(f.target.result, (img) => {
        img.scaleToWidth(200);
        fabricRef.current.add(img);
      });
    };
    reader.readAsDataURL(file);
  };

  const handleClear = () => {
    fabricRef.current.clear();
    fabricRef.current.setBackgroundColor('#ffffff', () => {});
    onUpdate();
  };

  return (
    <div>
      <div className="flex flex-wrap gap-2 mb-4">
        <button onClick={handleAddText} className="tool-btn bg-blue-600">Add Text</button>
        <button onClick={handleAddRect} className="tool-btn bg-green-600">Add Rectangle</button>
        <button onClick={handleAddCircle} className="tool-btn bg-indigo-600">Add Circle</button>

        <label className="tool-btn bg-yellow-600 cursor-pointer">
          Upload Image
          <input type="file" onChange={handleAddImage} className="hidden" accept="image/*" />
        </label>

        <button onClick={handleUndo} className="tool-btn bg-gray-500">Undo</button>
        <button onClick={handleRedo} className="tool-btn bg-gray-700">Redo</button>
        <button onClick={handleClear} className="tool-btn bg-red-600">Clear</button>
      </div>

      <div className="border shadow-md rounded overflow-hidden w-full max-w-[1024px]">
        <canvas ref={canvasEl} className="block" />
      </div>
    </div>
  );
};

export default CanvasEditor;
