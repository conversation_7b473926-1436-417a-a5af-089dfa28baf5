import React, { useEffect, useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

const ShirtModel = ({ canvas }) => {
  const { scene } = useGLTF('/models/tshirt.glb');
  const textureRef = useRef();

  useEffect(() => {
    if (!canvas) return;

    const tex = new THREE.CanvasTexture(canvas);
    tex.flipY = false;
    tex.needsUpdate = true;

    scene.traverse(child => {
      if (child.isMesh) {
        child.material.map = tex;
        child.material.needsUpdate = true;
      }
    });

    textureRef.current = tex;
  }, [canvas]);

  useFrame(() => {
    if (textureRef.current) {
      textureRef.current.needsUpdate = true;
    }
  });

  return <primitive object={scene} scale={1.5} />;
};

export default ShirtModel;
